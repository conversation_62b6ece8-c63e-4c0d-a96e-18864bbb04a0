@echo off
title Frappe Books - Build Application
color 0B

echo.
echo ========================================
echo    FRAPPE BOOKS - BUILD APPLICATION
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Please make sure you're running this from the Frappe Books directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Set Node.js path
set NODE_PATH=%TEMP%\nodejs\node-v20.18.1-win-x64
set NODE_EXE=%NODE_PATH%\node.exe
set YARN_CMD=%NODE_PATH%\yarn.cmd

echo Checking Node.js installation...
if not exist "%NODE_EXE%" (
    echo ERROR: Node.js v20.18.1 not found at %NODE_PATH%
    echo Please run the setup process first.
    pause
    exit /b 1
)

echo Node.js found: %NODE_EXE%
"%NODE_EXE%" --version

echo.
echo Checking Yarn installation...
if not exist "%YARN_CMD%" (
    echo ERROR: Yarn not found at %YARN_CMD%
    echo Please run the setup process first.
    pause
    exit /b 1
)

echo Yarn found: %YARN_CMD%
call "%YARN_CMD%" --version

echo.
echo ========================================
echo Building Frappe Books...
echo ========================================
echo.
echo This will create a production build of the application.
echo The build process may take several minutes.
echo.

REM Start the build process
call "%YARN_CMD%" build

echo.
echo ========================================
echo Build process completed.
echo ========================================
echo.
echo Check the dist_electron folder for build output.
pause
