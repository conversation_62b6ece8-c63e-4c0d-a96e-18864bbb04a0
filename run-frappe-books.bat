@echo off
title Frappe Books - Development Server
color 0A

echo.
echo ========================================
echo    FRAPPE BOOKS - DEVELOPMENT SERVER
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "package.json" (
    echo ERROR: package.json not found!
    echo Please make sure you're running this from the Frappe Books directory.
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Set Node.js path
set NODE_PATH=%TEMP%\nodejs\node-v20.18.1-win-x64
set NODE_EXE=%NODE_PATH%\node.exe
set YARN_CMD=%NODE_PATH%\yarn.cmd

echo Checking Node.js installation...
if not exist "%NODE_EXE%" (
    echo ERROR: Node.js v20.18.1 not found at %NODE_PATH%
    echo Please run the setup process first.
    pause
    exit /b 1
)

echo Node.js found: %NODE_EXE%
"%NODE_EXE%" --version

echo.
echo Checking Yarn installation...
if not exist "%YARN_CMD%" (
    echo ERROR: Yarn not found at %YARN_CMD%
    echo Please run the setup process first.
    pause
    exit /b 1
)

echo Yarn found: %YARN_CMD%
call "%YARN_CMD%" --version

echo.
echo ========================================
echo Starting Frappe Books Development Server...
echo ========================================
echo.
echo - Frontend will be available at: http://127.0.0.1:6969/
echo - Electron debugger at: ws://127.0.0.1:5858/
echo - Press Ctrl+C to stop the server
echo.



REM Start the development server
call "%YARN_CMD%" dev

echo.
echo ========================================
echo Development server stopped.
echo ========================================
pause
