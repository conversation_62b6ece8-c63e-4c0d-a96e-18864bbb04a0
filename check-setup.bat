@echo off
title Frappe Books - Setup Verification
color 0E

echo.
echo ========================================
echo    FRAPPE BOOKS - SETUP VERIFICATION
echo ========================================
echo.

REM Check if we're in the correct directory
echo Checking project directory...
if not exist "package.json" (
    echo [ERROR] package.json not found!
    echo Please make sure you're running this from the Frappe Books directory.
    echo Current directory: %CD%
    goto :end
) else (
    echo [OK] Found package.json
)

if not exist "src" (
    echo [ERROR] src directory not found!
    goto :end
) else (
    echo [OK] Found src directory
)

if not exist "node_modules" (
    echo [ERROR] node_modules directory not found!
    echo Please run 'yarn install' first.
    goto :end
) else (
    echo [OK] Found node_modules directory
)

echo.
echo Checking Node.js installation...
set NODE_PATH=%TEMP%\nodejs\node-v20.18.1-win-x64
set NODE_EXE=%NODE_PATH%\node.exe
set YARN_CMD=%NODE_PATH%\yarn.cmd

if not exist "%NODE_EXE%" (
    echo [ERROR] Node.js v20.18.1 not found at %NODE_PATH%
    echo Please run the setup process first.
    goto :end
) else (
    echo [OK] Found Node.js at %NODE_EXE%
    "%NODE_EXE%" --version
)

echo.
echo Checking Yarn installation...
if not exist "%YARN_CMD%" (
    echo [ERROR] Yarn not found at %YARN_CMD%
    echo Please run the setup process first.
    goto :end
) else (
    echo [OK] Found Yarn at %YARN_CMD%
    "%YARN_CMD%" --version
)

echo.
echo Checking critical dependencies...
if not exist "node_modules\better-sqlite3" (
    echo [ERROR] better-sqlite3 not found!
    echo Please run 'yarn install' to install dependencies.
    goto :end
) else (
    echo [OK] Found better-sqlite3
)

if not exist "node_modules\electron" (
    echo [ERROR] electron not found!
    echo Please run 'yarn install' to install dependencies.
    goto :end
) else (
    echo [OK] Found electron
)

if not exist "node_modules\vue" (
    echo [ERROR] vue not found!
    echo Please run 'yarn install' to install dependencies.
    goto :end
) else (
    echo [OK] Found vue
)

echo.
echo ========================================
echo    SETUP VERIFICATION COMPLETE
echo ========================================
echo.
echo [SUCCESS] All checks passed!
echo Frappe Books is ready to run.
echo.
echo Available commands:
echo - run-frappe-books.bat    : Start development server
echo - build-frappe-books.bat  : Build production version
echo - check-setup.bat         : Verify setup (this file)
echo.

:end
pause
