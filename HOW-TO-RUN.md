# How to Run Frappe Books

This guide explains how to use the batch files to run Frappe Books easily.

## 📁 Available Batch Files

### 1. `run-frappe-books.bat` 🚀
**Purpose**: Start the development server with hot-reload

**What it does**:
- Verifies Node.js and Yarn installation
- Starts the Vite development server
- Launches Electron application
- Enables hot-reload for development

**Usage**: Double-click the file or run from command prompt
```cmd
run-frappe-books.bat
```

**Access**:
- Application: Opens automatically in Electron window
- Web interface: http://127.0.0.1:6969/
- Debug tools: ws://127.0.0.1:5858/

---

### 2. `build-frappe-books.bat` 🔨
**Purpose**: Build production version of the application

**What it does**:
- Compiles the frontend with Vite
- Packages the Electron application
- Creates distributable files

**Usage**: Double-click the file or run from command prompt
```cmd
build-frappe-books.bat
```

**Output**: Check `dist_electron` folder for build results

---

### 3. `check-setup.bat` ✅
**Purpose**: Verify that everything is set up correctly

**What it does**:
- Checks if all required files exist
- Verifies Node.js v20.18.1 installation
- Confirms Yarn is available
- Validates critical dependencies

**Usage**: Double-click the file or run from command prompt
```cmd
check-setup.bat
```

## 🛠 Troubleshooting

### If `run-frappe-books.bat` fails:
1. Run `check-setup.bat` first to verify installation
2. Make sure you're in the correct directory (where package.json exists)
3. Check that Node.js v20.18.1 is installed in the temp directory

### If `build-frappe-books.bat` fails:
1. Close any running Frappe Books instances
2. Delete the `dist_electron` folder if it exists
3. Run the build again

### If `check-setup.bat` shows errors:
1. Re-run the setup process
2. Make sure all dependencies are installed with `yarn install`
3. Verify Node.js v20.18.1 is properly installed

## 📝 Notes

- **Node.js Location**: These batch files use Node.js v20.18.1 installed at:
  `%TEMP%\nodejs\node-v20.18.1-win-x64\`

- **Development vs Production**: 
  - Use `run-frappe-books.bat` for development (with hot-reload)
  - Use `build-frappe-books.bat` to create production builds

- **First Time Setup**: If this is your first time, run `check-setup.bat` to verify everything is working

## 🚀 Quick Start

1. **Verify Setup**: Double-click `check-setup.bat`
2. **Start Development**: Double-click `run-frappe-books.bat`
3. **Start Using**: The application will open automatically!

---

**Happy coding with Frappe Books! 📚✨**
